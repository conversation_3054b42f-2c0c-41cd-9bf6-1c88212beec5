import '../../../../core/base/usecase.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing in with Google
class SignInWithGoogleUseCase implements UseCase<AuthResult, NoParams> {
  const SignInWithGoogleUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, AuthResult>> call(NoParams params) async {
    try {
      final result = await _repository.signInWithGoogle();
      return Right(result);
    } catch (e) {
      return Left(
        AuthFailure(
          message: '<PERSON><PERSON> masuk dengan <PERSON>',
          details: e.toString(),
        ),
      );
    }
  }
}
