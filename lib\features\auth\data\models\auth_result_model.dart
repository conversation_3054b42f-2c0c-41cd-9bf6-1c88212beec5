import '../../domain/entities/auth_result.dart';
import '../../domain/entities/user_entity.dart';

/// Authentication result model for data layer
class AuthResultModel {
  const AuthResultModel({
    required this.user,
    required this.isNewUser,
    this.requiresPasswordChange = false,
    this.temporaryPassword,
  });

  final UserEntity user;
  final bool isNewUser;
  final bool requiresPasswordChange;
  final String? temporaryPassword;

  /// Create AuthResultModel from AuthResult entity
  factory AuthResultModel.fromEntity(AuthResult entity) {
    return AuthResultModel(
      user: entity.user,
      isNewUser: entity.isNewUser,
      requiresPasswordChange: entity.requiresPasswordChange,
      temporaryPassword: entity.temporaryPassword,
    );
  }

  /// Convert to AuthResult entity
  AuthResult toEntity() {
    return AuthResult(
      user: user,
      isNewUser: isNewUser,
      requiresPasswordChange: requiresPasswordChange,
      temporaryPassword: temporaryPassword,
    );
  }

  AuthResultModel copyWith({
    UserEntity? user,
    bool? isNewUser,
    bool? requiresPasswordChange,
    String? temporaryPassword,
  }) {
    return AuthResultModel(
      user: user ?? this.user,
      isNewUser: isNewUser ?? this.isNewUser,
      requiresPasswordChange:
          requiresPasswordChange ?? this.requiresPasswordChange,
      temporaryPassword: temporaryPassword ?? this.temporaryPassword,
    );
  }
}

/// Sign in credentials model
class SignInCredentialsModel {
  const SignInCredentialsModel({required this.email, required this.password});

  final String email;
  final String password;

  /// Create SignInCredentialsModel from SignInCredentials entity
  factory SignInCredentialsModel.fromEntity(SignInCredentials entity) {
    return SignInCredentialsModel(
      email: entity.email,
      password: entity.password,
    );
  }

  /// Convert to SignInCredentials entity
  SignInCredentials toEntity() {
    return SignInCredentials(email: email, password: password);
  }
}

/// Password reset request model
class PasswordResetRequestModel {
  const PasswordResetRequestModel({required this.email});

  final String email;

  /// Create PasswordResetRequestModel from PasswordResetRequest entity
  factory PasswordResetRequestModel.fromEntity(PasswordResetRequest entity) {
    return PasswordResetRequestModel(email: entity.email);
  }

  /// Convert to PasswordResetRequest entity
  PasswordResetRequest toEntity() {
    return PasswordResetRequest(email: email);
  }
}

/// Password update request model
class PasswordUpdateRequestModel {
  const PasswordUpdateRequestModel({
    required this.currentPassword,
    required this.newPassword,
  });

  final String currentPassword;
  final String newPassword;

  /// Create PasswordUpdateRequestModel from PasswordUpdateRequest entity
  factory PasswordUpdateRequestModel.fromEntity(PasswordUpdateRequest entity) {
    return PasswordUpdateRequestModel(
      currentPassword: entity.currentPassword,
      newPassword: entity.newPassword,
    );
  }

  /// Convert to PasswordUpdateRequest entity
  PasswordUpdateRequest toEntity() {
    return PasswordUpdateRequest(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
  }
}

/// Registration credentials model
class RegistrationCredentialsModel {
  const RegistrationCredentialsModel({
    required this.email,
    required this.password,
    required this.displayName,
    this.phoneNumber,
  });

  final String email;
  final String password;
  final String displayName;
  final String? phoneNumber;

  /// Create RegistrationCredentialsModel from RegistrationCredentials entity
  factory RegistrationCredentialsModel.fromEntity(
    RegistrationCredentials entity,
  ) {
    return RegistrationCredentialsModel(
      email: entity.email,
      password: entity.password,
      displayName: entity.displayName,
      phoneNumber: entity.phoneNumber,
    );
  }

  /// Convert to RegistrationCredentials entity
  RegistrationCredentials toEntity() {
    return RegistrationCredentials(
      email: email,
      password: password,
      displayName: displayName,
      phoneNumber: phoneNumber,
    );
  }
}
