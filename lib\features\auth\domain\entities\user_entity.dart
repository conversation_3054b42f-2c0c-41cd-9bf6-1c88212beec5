import 'package:equatable/equatable.dart';

/// User entity representing a user in the domain layer
class UserEntity extends Equatable {
  const UserEntity({
    required this.uid,
    required this.email,
    required this.displayName,
    required this.role,
    required this.status,
    required this.authMethod,
    required this.passwordChangeRequired,
    required this.createdAt,
    this.createdBy,
    this.lastLogin,
    this.department,
    this.profilePicture,
    this.preferences,
  });

  final String uid;
  final String email;
  final String displayName;
  final UserRole role;
  final UserStatus status;
  final AuthMethod authMethod;
  final bool passwordChangeRequired;
  final DateTime createdAt;
  final String? createdBy;
  final DateTime? lastLogin;
  final String? department;
  final String? profilePicture;
  final UserPreferences? preferences;

  @override
  List<Object?> get props => [
        uid,
        email,
        displayName,
        role,
        status,
        authMethod,
        passwordChangeRequired,
        createdAt,
        createdBy,
        lastLogin,
        department,
        profilePicture,
        preferences,
      ];

  UserEntity copyWith({
    String? uid,
    String? email,
    String? displayName,
    UserRole? role,
    UserStatus? status,
    AuthMethod? authMethod,
    bool? passwordChangeRequired,
    DateTime? createdAt,
    String? createdBy,
    DateTime? lastLogin,
    String? department,
    String? profilePicture,
    UserPreferences? preferences,
  }) {
    return UserEntity(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      role: role ?? this.role,
      status: status ?? this.status,
      authMethod: authMethod ?? this.authMethod,
      passwordChangeRequired: passwordChangeRequired ?? this.passwordChangeRequired,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      lastLogin: lastLogin ?? this.lastLogin,
      department: department ?? this.department,
      profilePicture: profilePicture ?? this.profilePicture,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// User role enumeration
enum UserRole {
  admin,
  user;

  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.user:
        return 'User';
    }
  }

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return UserRole.admin;
      case 'user':
        return UserRole.user;
      default:
        return UserRole.user;
    }
  }
}

/// User status enumeration
enum UserStatus {
  active,
  inactive,
  deleted;

  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.inactive:
        return 'Inactive';
      case UserStatus.deleted:
        return 'Deleted';
    }
  }

  static UserStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return UserStatus.active;
      case 'inactive':
        return UserStatus.inactive;
      case 'deleted':
        return UserStatus.deleted;
      default:
        return UserStatus.active;
    }
  }
}

/// Authentication method enumeration
enum AuthMethod {
  email,
  google;

  String get displayName {
    switch (this) {
      case AuthMethod.email:
        return 'Email/Password';
      case AuthMethod.google:
        return 'Google Sign-In';
    }
  }

  static AuthMethod fromString(String method) {
    switch (method.toLowerCase()) {
      case 'email':
        return AuthMethod.email;
      case 'google':
        return AuthMethod.google;
      default:
        return AuthMethod.email;
    }
  }
}

/// User preferences entity
class UserPreferences extends Equatable {
  const UserPreferences({
    this.theme = 'light',
    this.language = 'id',
    this.emailNotifications = true,
  });

  final String theme;
  final String language;
  final bool emailNotifications;

  @override
  List<Object?> get props => [theme, language, emailNotifications];

  UserPreferences copyWith({
    String? theme,
    String? language,
    bool? emailNotifications,
  }) {
    return UserPreferences(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      emailNotifications: emailNotifications ?? this.emailNotifications,
    );
  }
}
