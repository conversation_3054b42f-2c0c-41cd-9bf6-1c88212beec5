import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/firebase_service.dart';
import '../../core/services/auth_service.dart';
import '../../core/utils/logger.dart';
import 'auth_providers.dart';

/// App initialization state
enum AppInitializationState {
  initial,
  initializing,
  initialized,
  error,
}

/// App initialization data
class AppInitializationData {
  const AppInitializationData({
    required this.state,
    this.error,
    this.progress = 0.0,
    this.currentStep = '',
  });

  final AppInitializationState state;
  final String? error;
  final double progress;
  final String currentStep;

  AppInitializationData copyWith({
    AppInitializationState? state,
    String? error,
    double? progress,
    String? currentStep,
  }) {
    return AppInitializationData(
      state: state ?? this.state,
      error: error ?? this.error,
      progress: progress ?? this.progress,
      currentStep: currentStep ?? this.currentStep,
    );
  }

  bool get isInitialized => state == AppInitializationState.initialized;
  bool get isInitializing => state == AppInitializationState.initializing;
  bool get hasError => state == AppInitializationState.error;
}

/// App initialization notifier
class AppInitializationNotifier extends StateNotifier<AppInitializationData> {
  AppInitializationNotifier() : super(
    const AppInitializationData(state: AppInitializationState.initial),
  );

  /// Initialize the application
  Future<void> initialize() async {
    if (state.isInitialized || state.isInitializing) return;

    state = state.copyWith(
      state: AppInitializationState.initializing,
      progress: 0.0,
      currentStep: 'Starting initialization...',
    );

    try {
      // Step 1: Initialize Firebase
      state = state.copyWith(
        progress: 0.2,
        currentStep: 'Initializing Firebase...',
      );
      
      await FirebaseService.instance.initialize();
      AppLogger.success('Firebase initialized');

      // Step 2: Initialize Auth Service
      state = state.copyWith(
        progress: 0.4,
        currentStep: 'Initializing Authentication...',
      );
      
      await AuthService.instance.initialize();
      AppLogger.success('Auth Service initialized');

      // Step 3: Load user preferences
      state = state.copyWith(
        progress: 0.6,
        currentStep: 'Loading user preferences...',
      );
      
      // User preferences will be loaded by UserPreferencesNotifier
      await Future.delayed(const Duration(milliseconds: 500));
      AppLogger.success('User preferences loaded');

      // Step 4: Check authentication state
      state = state.copyWith(
        progress: 0.8,
        currentStep: 'Checking authentication state...',
      );
      
      // Authentication state will be handled by auth providers
      await Future.delayed(const Duration(milliseconds: 500));
      AppLogger.success('Authentication state checked');

      // Step 5: Complete initialization
      state = state.copyWith(
        progress: 1.0,
        currentStep: 'Initialization complete',
        state: AppInitializationState.initialized,
      );
      
      AppLogger.success('App initialization completed successfully');

    } catch (e, stackTrace) {
      AppLogger.error('App initialization failed', e, stackTrace);
      
      state = state.copyWith(
        state: AppInitializationState.error,
        error: e.toString(),
        currentStep: 'Initialization failed',
      );
    }
  }

  /// Retry initialization
  Future<void> retry() async {
    state = const AppInitializationData(state: AppInitializationState.initial);
    await initialize();
  }

  /// Reset initialization state
  void reset() {
    state = const AppInitializationData(state: AppInitializationState.initial);
  }
}

/// Provider for app initialization
final appInitializationProvider = StateNotifierProvider<AppInitializationNotifier, AppInitializationData>((ref) {
  return AppInitializationNotifier();
});

/// Provider for checking if app is initialized
final isAppInitializedProvider = Provider<bool>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.isInitialized;
});

/// Provider for checking if app is initializing
final isAppInitializingProvider = Provider<bool>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.isInitializing;
});

/// Provider for app initialization progress
final appInitializationProgressProvider = Provider<double>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.progress;
});

/// Provider for current initialization step
final currentInitializationStepProvider = Provider<String>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.currentStep;
});

/// Provider for initialization error
final initializationErrorProvider = Provider<String?>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.error;
});

/// Provider for checking if initialization has error
final hasInitializationErrorProvider = Provider<bool>((ref) {
  final initData = ref.watch(appInitializationProvider);
  return initData.hasError;
});

/// Auto-initialize provider that starts initialization when first accessed
final autoInitializeProvider = FutureProvider<void>((ref) async {
  final notifier = ref.read(appInitializationProvider.notifier);
  await notifier.initialize();
});

/// Provider that combines auth state and initialization state
final appReadyProvider = Provider<bool>((ref) {
  final isInitialized = ref.watch(isAppInitializedProvider);
  final authState = ref.watch(authStateProvider);
  
  // App is ready when initialized and auth state is resolved (either signed in or not)
  return isInitialized && !authState.isLoading;
});

/// Provider for getting app status message
final appStatusMessageProvider = Provider<String>((ref) {
  final initData = ref.watch(appInitializationProvider);
  final authState = ref.watch(authStateProvider);
  
  if (initData.hasError) {
    return 'Initialization failed: ${initData.error}';
  }
  
  if (initData.isInitializing) {
    return initData.currentStep;
  }
  
  if (!initData.isInitialized) {
    return 'App not initialized';
  }
  
  return authState.when(
    data: (user) => user != null ? 'Signed in as ${user.displayName}' : 'Ready to sign in',
    loading: () => 'Loading authentication state...',
    error: (error, _) => 'Authentication error: $error',
  );
});
