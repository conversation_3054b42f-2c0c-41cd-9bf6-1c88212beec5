import '../../domain/entities/auth_result.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/auth_result_model.dart';
import '../models/user_model.dart';

/// Implementation of AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  const AuthRepositoryImpl({required this.remoteDataSource});

  final AuthRemoteDataSource remoteDataSource;

  @override
  UserEntity? get currentUser => remoteDataSource.currentUser?.toEntity();

  @override
  Stream<UserEntity?> get authStateChanges {
    return remoteDataSource.authStateChanges.map((user) => user?.toEntity());
  }

  @override
  bool get isSignedIn => remoteDataSource.isSignedIn;

  @override
  Future<AuthResult> signInWithEmailAndPassword(
    SignInCredentials credentials,
  ) async {
    final credentialsModel = SignInCredentialsModel.fromEntity(credentials);
    final result = await remoteDataSource.signInWithEmailAndPassword(
      credentialsModel,
    );
    return result.toEntity();
  }

  @override
  Future<AuthResult> signInWithGoogle() async {
    final resultModel = await remoteDataSource.signInWithGoogle();
    return resultModel.toEntity();
  }

  @override
  Future<AuthResult> registerWithEmailAndPassword(
    RegistrationCredentials credentials,
  ) async {
    final credentialsModel = RegistrationCredentialsModel.fromEntity(
      credentials,
    );
    final resultModel = await remoteDataSource.registerWithEmailAndPassword(
      credentialsModel,
    );
    return resultModel.toEntity();
  }

  @override
  Future<void> signOut() async {
    await remoteDataSource.signOut();
  }

  @override
  Future<void> sendPasswordResetEmail(PasswordResetRequest request) async {
    final requestModel = PasswordResetRequestModel.fromEntity(request);
    await remoteDataSource.sendPasswordResetEmail(requestModel);
  }

  @override
  Future<void> updatePassword(PasswordUpdateRequest request) async {
    final requestModel = PasswordUpdateRequestModel.fromEntity(request);
    await remoteDataSource.updatePassword(requestModel);
  }

  @override
  Future<UserEntity?> getUserProfile(String uid) async {
    final userModel = await remoteDataSource.getUserProfile(uid);
    return userModel?.toEntity();
  }

  @override
  Future<void> updateUserProfile(UserEntity user) async {
    final userModel = UserModel.fromEntity(user);
    await remoteDataSource.updateUserProfile(userModel);
  }

  @override
  Future<UserEntity?> refreshCurrentUser() async {
    final userModel = await remoteDataSource.refreshCurrentUser();
    return userModel?.toEntity();
  }

  @override
  Future<void> deleteAccount() async {
    await remoteDataSource.deleteAccount();
  }

  @override
  Future<void> reauthenticateWithPassword(String password) async {
    await remoteDataSource.reauthenticateWithPassword(password);
  }

  @override
  Future<void> reauthenticateWithGoogle() async {
    await remoteDataSource.reauthenticateWithGoogle();
  }

  @override
  Future<bool> isEmailApproved(String email) async {
    return await remoteDataSource.isEmailApproved(email);
  }

  @override
  Future<void> sendEmailVerification() async {
    await remoteDataSource.sendEmailVerification();
  }

  @override
  bool get isEmailVerified => remoteDataSource.isEmailVerified;

  @override
  Future<void> reloadUser() async {
    await remoteDataSource.reloadUser();
  }
}
