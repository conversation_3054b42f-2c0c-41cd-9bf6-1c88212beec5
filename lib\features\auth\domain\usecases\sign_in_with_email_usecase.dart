import '../../../../core/base/usecase.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_result.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing in with email and password
class SignInWithEmailUseCase implements UseCase<AuthResult, SignInCredentials> {
  const SignInWithEmailUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, AuthResult>> call(SignInCredentials params) async {
    try {
      // Validate input
      final validationResult = _validateCredentials(params);
      if (validationResult != null) {
        return Left(validationResult);
      }

      // Attempt sign in
      final result = await _repository.signInWithEmailAndPassword(params);

      return Right(result);
    } catch (e) {
      return Left(
        AuthFailure(
          message: '<PERSON>l masuk dengan email dan password',
          details: e.toString(),
        ),
      );
    }
  }

  /// Validate sign in credentials
  Failure? _validateCredentials(SignInCredentials credentials) {
    // Validate email
    if (credentials.email.isEmpty) {
      return const ValidationFailure(
        message: 'Email tidak boleh kosong',
        code: 'email_empty',
      );
    }

    if (!_isValidEmail(credentials.email)) {
      return const ValidationFailure(
        message: 'Format email tidak valid',
        code: 'email_invalid',
      );
    }

    // Validate password
    if (credentials.password.isEmpty) {
      return const ValidationFailure(
        message: 'Password tidak boleh kosong',
        code: 'password_empty',
      );
    }

    if (credentials.password.length < 6) {
      return const ValidationFailure(
        message: 'Password minimal 6 karakter',
        code: 'password_too_short',
      );
    }

    return null;
  }

  /// Check if email format is valid
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
