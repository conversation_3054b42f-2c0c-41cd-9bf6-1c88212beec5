import 'package:equatable/equatable.dart';
import 'user_entity.dart';

/// Authentication result entity
class AuthResult extends Equatable {
  const AuthResult({
    required this.user,
    required this.isNewUser,
    this.requiresPasswordChange = false,
    this.temporaryPassword,
  });

  final UserEntity user;
  final bool isNewUser;
  final bool requiresPasswordChange;
  final String? temporaryPassword;

  @override
  List<Object?> get props => [
    user,
    isNewUser,
    requiresPasswordChange,
    temporaryPassword,
  ];

  AuthResult copyWith({
    UserEntity? user,
    bool? isNewUser,
    bool? requiresPasswordChange,
    String? temporaryPassword,
  }) {
    return AuthResult(
      user: user ?? this.user,
      isNewUser: isNewUser ?? this.isNewUser,
      requiresPasswordChange:
          requiresPasswordChange ?? this.requiresPasswordChange,
      temporaryPassword: temporaryPassword ?? this.temporaryPassword,
    );
  }
}

/// Authentication state enumeration
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error;

  bool get isLoading => this == AuthState.loading;
  bool get isAuthenticated => this == AuthState.authenticated;
  bool get isUnauthenticated => this == AuthState.unauthenticated;
  bool get hasError => this == AuthState.error;
}

/// Sign in credentials
class SignInCredentials extends Equatable {
  const SignInCredentials({required this.email, required this.password});

  final String email;
  final String password;

  @override
  List<Object?> get props => [email, password];
}

/// Password reset request
class PasswordResetRequest extends Equatable {
  const PasswordResetRequest({required this.email});

  final String email;

  @override
  List<Object?> get props => [email];
}

/// Registration credentials
class RegistrationCredentials extends Equatable {
  const RegistrationCredentials({
    required this.email,
    required this.password,
    required this.displayName,
    this.phoneNumber,
  });

  final String email;
  final String password;
  final String displayName;
  final String? phoneNumber;

  @override
  List<Object?> get props => [email, password, displayName, phoneNumber];
}

/// Password update request
class PasswordUpdateRequest extends Equatable {
  const PasswordUpdateRequest({
    required this.currentPassword,
    required this.newPassword,
  });

  final String currentPassword;
  final String newPassword;

  @override
  List<Object?> get props => [currentPassword, newPassword];
}
