import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Authentication header with logo and title
class AuthHeader extends StatelessWidget {
  const AuthHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > AppConstants.tabletBreakpoint;
    final isDesktop = screenSize.width > AppConstants.desktopBreakpoint;

    return Column(
      children: [
        // Logo
        Container(
          width: isDesktop ? 120.0 : isTablet ? 100.0 : 80.0,
          height: isDesktop ? 120.0 : isTablet ? 100.0 : 80.0,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(20.0),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                blurRadius: 20.0,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Icon(
            Icons.description_outlined,
            size: isDesktop ? 60.0 : isTablet ? 50.0 : 40.0,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        
        SizedBox(height: isTablet ? 24.0 : 16.0),
        
        // App title
        Text(
          AppConstants.appName,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8.0),
        
        // App subtitle
        Text(
          'Sistem Manajemen Dokumen',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 4.0),
        
        // Organization name
        Text(
          'Balai Penyuluhan Pertanian',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
