import '../../../../core/base/usecase.dart';
import '../../../../core/errors/failures.dart';
import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Use case for sending password reset email
class SendPasswordResetUseCase implements UseCase<void, PasswordResetRequest> {
  const SendPasswordResetUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, void>> call(PasswordResetRequest params) async {
    try {
      // Validate email
      final validationResult = _validateEmail(params.email);
      if (validationResult != null) {
        return Left(validationResult);
      }

      await _repository.sendPasswordResetEmail(params);
      return const Right(null);
    } catch (e) {
      return Left(
        AuthFailure(
          message: 'Gagal mengirim email reset password',
          details: e.toString(),
        ),
      );
    }
  }

  /// Validate email format
  Failure? _validateEmail(String email) {
    if (email.isEmpty) {
      return const ValidationFailure(
        message: 'Email tidak boleh kosong',
        code: 'email_empty',
      );
    }

    if (!_isValidEmail(email)) {
      return const ValidationFailure(
        message: 'Format email tidak valid',
        code: 'email_invalid',
      );
    }

    return null;
  }

  /// Check if email format is valid
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
