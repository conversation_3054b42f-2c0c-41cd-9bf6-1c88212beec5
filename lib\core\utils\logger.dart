import 'package:logger/logger.dart';

/// Custom logger for the application
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  // Private constructor to prevent instantiation
  AppLogger._();

  /// Log debug message
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log success message
  static void success(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log verbose message (only in debug mode)
  static void verbose(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.t(message, error: error, stackTrace: stackTrace);
  }

  /// Log API request
  static void apiRequest(
    String method,
    String url, [
    Map<String, dynamic>? data,
  ]) {
    info('API Request: $method $url', data);
  }

  /// Log API response
  static void apiResponse(
    String method,
    String url,
    int statusCode, [
    dynamic data,
  ]) {
    info('API Response: $method $url - Status: $statusCode', data);
  }

  /// Log API error
  static void apiError(
    String method,
    String url,
    dynamic error, [
    StackTrace? stackTrace,
  ]) {
    AppLogger.error('API Error: $method $url', error, stackTrace);
  }

  /// Log user action
  static void userAction(String action, [Map<String, dynamic>? data]) {
    info('User Action: $action', data);
  }

  /// Log authentication event
  static void auth(String event, [Map<String, dynamic>? data]) {
    info('Auth Event: $event', data);
  }

  /// Log file operation
  static void fileOperation(
    String operation,
    String fileName, [
    Map<String, dynamic>? data,
  ]) {
    info('File Operation: $operation - $fileName', data);
  }

  /// Log database operation
  static void database(
    String operation,
    String collection, [
    Map<String, dynamic>? data,
  ]) {
    info('Database Operation: $operation - $collection', data);
  }

  /// Log navigation event
  static void navigation(String from, String to, [Map<String, dynamic>? data]) {
    info('Navigation: $from -> $to', data);
  }

  /// Log performance metric
  static void performance(
    String metric,
    Duration duration, [
    Map<String, dynamic>? data,
  ]) {
    info('Performance: $metric took ${duration.inMilliseconds}ms', data);
  }

  /// Log security event
  static void security(String event, [Map<String, dynamic>? data]) {
    warning('Security Event: $event', data);
  }

  /// Log business logic event
  static void business(String event, [Map<String, dynamic>? data]) {
    info('Business Event: $event', data);
  }
}
