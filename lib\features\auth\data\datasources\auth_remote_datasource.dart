import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../models/auth_result_model.dart';
import '../../domain/entities/user_entity.dart';

/// Remote data source for authentication operations
abstract class AuthRemoteDataSource {
  /// Get current authenticated user
  UserModel? get currentUser;

  /// Stream of authentication state changes
  Stream<UserModel?> get authStateChanges;

  /// Check if user is currently signed in
  bool get isSignedIn;

  /// Sign in with email and password
  Future<AuthResultModel> signInWithEmailAndPassword(
    SignInCredentialsModel credentials,
  );

  /// Sign in with Google
  Future<AuthResultModel> signInWithGoogle();

  /// Register new user with email and password
  Future<AuthResultModel> registerWithEmailAndPassword(
    RegistrationCredentialsModel credentials,
  );

  /// Sign out current user
  Future<void> signOut();

  /// Send password reset email
  Future<void> sendPasswordResetEmail(PasswordResetRequestModel request);

  /// Update user password
  Future<void> updatePassword(PasswordUpdateRequestModel request);

  /// Get user profile data
  Future<UserModel?> getUserProfile(String uid);

  /// Update user profile
  Future<void> updateUserProfile(UserModel user);

  /// Refresh current user data
  Future<UserModel?> refreshCurrentUser();

  /// Delete user account
  Future<void> deleteAccount();

  /// Re-authenticate user with password
  Future<void> reauthenticateWithPassword(String password);

  /// Re-authenticate with Google
  Future<void> reauthenticateWithGoogle();

  /// Check if email is approved for registration
  Future<bool> isEmailApproved(String email);

  /// Send email verification
  Future<void> sendEmailVerification();

  /// Check if email is verified
  bool get isEmailVerified;

  /// Reload current user data from Firebase
  Future<void> reloadUser();
}

/// Implementation of AuthRemoteDataSource
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl({
    required this.authService,
    required this.firestore,
  });

  final AuthService authService;
  final FirebaseFirestore firestore;

  @override
  UserModel? get currentUser {
    final user = authService.currentUser;
    if (user == null) return null;

    // Convert Firebase User to UserModel
    // Note: This is a basic conversion, full user data should be fetched from Firestore
    return UserModel(
      uid: user.uid,
      email: user.email ?? '',
      displayName: user.displayName ?? '',
      role: UserRole.user, // Default role, should be fetched from Firestore
      status: UserStatus.active,
      authMethod: _getAuthMethod(user),
      passwordChangeRequired: false,
      createdAt: user.metadata.creationTime ?? DateTime.now(),
      lastLogin: user.metadata.lastSignInTime,
      profilePicture: user.photoURL,
    );
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return authService.authStateChanges.asyncMap((user) async {
      if (user == null) return null;

      // Fetch full user data from Firestore
      try {
        final userDoc = await firestore.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
          return UserModel.fromFirestore(userDoc.data()!);
        }
      } catch (e) {
        AppLogger.error('Failed to fetch user data from Firestore', e);
      }

      // Fallback to basic user data from Firebase Auth
      return UserModel(
        uid: user.uid,
        email: user.email ?? '',
        displayName: user.displayName ?? '',
        role: UserRole.user,
        status: UserStatus.active,
        authMethod: _getAuthMethod(user),
        passwordChangeRequired: false,
        createdAt: user.metadata.creationTime ?? DateTime.now(),
        lastLogin: user.metadata.lastSignInTime,
        profilePicture: user.photoURL,
      );
    });
  }

  @override
  bool get isSignedIn => authService.isSignedIn;

  @override
  Future<AuthResultModel> signInWithEmailAndPassword(
    SignInCredentialsModel credentials,
  ) async {
    try {
      final userCredential = await authService.signInWithEmailAndPassword(
        email: credentials.email,
        password: credentials.password,
      );

      final user = userCredential.user;
      if (user == null) {
        throw AuthException(
          message: 'Gagal mendapatkan data pengguna setelah login',
          code: 'user_null',
        );
      }

      // Fetch user data from Firestore
      final userModel = await _fetchUserFromFirestore(user.uid);

      return AuthResultModel(
        user: userModel.toEntity(),
        isNewUser: userCredential.additionalUserInfo?.isNewUser ?? false,
        requiresPasswordChange: userModel.passwordChangeRequired,
      );
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException(
        message: 'Gagal masuk dengan email dan password',
        details: e,
      );
    }
  }

  @override
  Future<AuthResultModel> signInWithGoogle() async {
    try {
      final userCredential = await authService.signInWithGoogle();

      final user = userCredential.user;
      if (user == null) {
        throw AuthException(
          message: 'Gagal mendapatkan data pengguna setelah login Google',
          code: 'user_null',
        );
      }

      // Fetch user data from Firestore
      final userModel = await _fetchUserFromFirestore(user.uid);

      return AuthResultModel(
        user: userModel.toEntity(),
        isNewUser: userCredential.additionalUserInfo?.isNewUser ?? false,
        requiresPasswordChange:
            false, // Google users don't need password change
      );
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException(message: 'Gagal masuk dengan Google', details: e);
    }
  }

  @override
  Future<AuthResultModel> registerWithEmailAndPassword(
    RegistrationCredentialsModel credentials,
  ) async {
    try {
      // Check if email is approved for registration
      final isApproved = await isEmailApproved(credentials.email);
      if (!isApproved) {
        throw AuthException(
          message:
              'Email tidak disetujui untuk registrasi. Hubungi administrator.',
        );
      }

      // Create user with Firebase Auth
      final userCredential = await authService.createUserWithEmailAndPassword(
        credentials.email,
        credentials.password,
      );

      if (userCredential.user == null) {
        throw const AuthException(message: 'Gagal membuat akun pengguna');
      }

      // Update display name
      await userCredential.user!.updateDisplayName(credentials.displayName);

      // Send email verification
      await userCredential.user!.sendEmailVerification();

      // Create user document in Firestore
      final userModel = UserModel(
        uid: userCredential.user!.uid,
        email: credentials.email,
        displayName: credentials.displayName,
        role: UserRole.user,
        status: UserStatus.active,
        authMethod: AuthMethod.email,
        passwordChangeRequired: false,
        createdAt: DateTime.now(),
        lastLogin: DateTime.now(),
        profilePicture: null,
        preferences: const UserPreferencesModel(
          theme: 'light',
          language: 'id',
          emailNotifications: true,
        ),
      );

      await firestore
          .collection('users')
          .doc(userCredential.user!.uid)
          .set(userModel.toFirestore());

      AppLogger.info('User registered successfully: ${credentials.email}');

      return AuthResultModel(
        user: userModel.toEntity(),
        isNewUser: true,
        requiresPasswordChange: false,
      );
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException(message: 'Gagal mendaftar akun baru', details: e);
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await authService.signOut();
    } catch (e) {
      throw AuthException(message: 'Gagal keluar dari aplikasi', details: e);
    }
  }

  @override
  Future<void> sendPasswordResetEmail(PasswordResetRequestModel request) async {
    try {
      await authService.sendPasswordResetEmail(request.email);
    } catch (e) {
      throw AuthException(
        message: 'Gagal mengirim email reset password',
        details: e,
      );
    }
  }

  @override
  Future<void> updatePassword(PasswordUpdateRequestModel request) async {
    try {
      await authService.updatePassword(request.newPassword);
    } catch (e) {
      throw AuthException(message: 'Gagal mengubah password', details: e);
    }
  }

  @override
  Future<UserModel?> getUserProfile(String uid) async {
    try {
      return await _fetchUserFromFirestore(uid);
    } catch (e) {
      throw AuthException(
        message: 'Gagal mendapatkan profil pengguna',
        details: e,
      );
    }
  }

  @override
  Future<void> updateUserProfile(UserModel user) async {
    try {
      await firestore
          .collection('users')
          .doc(user.uid)
          .update(user.toFirestore());
    } catch (e) {
      throw AuthException(
        message: 'Gagal memperbarui profil pengguna',
        details: e,
      );
    }
  }

  @override
  Future<UserModel?> refreshCurrentUser() async {
    final user = authService.currentUser;
    if (user == null) return null;

    try {
      await user.reload();
      return await _fetchUserFromFirestore(user.uid);
    } catch (e) {
      throw AuthException(
        message: 'Gagal memperbarui data pengguna',
        details: e,
      );
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      final user = authService.currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      // Delete user data from Firestore
      await firestore.collection('users').doc(user.uid).delete();

      // Delete Firebase Auth account
      await user.delete();
    } catch (e) {
      throw AuthException(message: 'Gagal menghapus akun', details: e);
    }
  }

  @override
  Future<void> reauthenticateWithPassword(String password) async {
    try {
      final user = authService.currentUser;
      if (user == null || user.email == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );

      await user.reauthenticateWithCredential(credential);
    } catch (e) {
      throw AuthException(
        message: 'Gagal melakukan autentikasi ulang',
        details: e,
      );
    }
  }

  @override
  Future<void> reauthenticateWithGoogle() async {
    try {
      final googleSignIn = GoogleSignIn();
      final googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        throw AuthException(
          message: 'Proses autentikasi Google dibatalkan',
          code: 'sign_in_cancelled',
        );
      }

      final googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final user = authService.currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      await user.reauthenticateWithCredential(credential);
    } catch (e) {
      throw AuthException(
        message: 'Gagal melakukan autentikasi ulang dengan Google',
        details: e,
      );
    }
  }

  @override
  Future<bool> isEmailApproved(String email) async {
    try {
      final doc = await firestore.collection('approvedEmails').doc(email).get();
      return doc.exists;
    } catch (e) {
      AppLogger.error('Failed to check if email is approved', e);
      return false;
    }
  }

  @override
  Future<void> sendEmailVerification() async {
    try {
      final user = authService.currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      await user.sendEmailVerification();
    } catch (e) {
      throw AuthException(
        message: 'Gagal mengirim email verifikasi',
        details: e,
      );
    }
  }

  @override
  bool get isEmailVerified => authService.currentUser?.emailVerified ?? false;

  @override
  Future<void> reloadUser() async {
    try {
      final user = authService.currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      await user.reload();
    } catch (e) {
      throw AuthException(
        message: 'Gagal memuat ulang data pengguna',
        details: e,
      );
    }
  }

  /// Fetch user data from Firestore
  Future<UserModel> _fetchUserFromFirestore(String uid) async {
    final userDoc = await firestore.collection('users').doc(uid).get();

    if (!userDoc.exists) {
      throw AuthException(
        message: 'Data pengguna tidak ditemukan',
        code: 'user_not_found',
      );
    }

    return UserModel.fromFirestore(userDoc.data()!);
  }

  /// Get authentication method from Firebase User
  AuthMethod _getAuthMethod(User user) {
    if (user.providerData.any((info) => info.providerId == 'google.com')) {
      return AuthMethod.google;
    }
    return AuthMethod.email;
  }
}
