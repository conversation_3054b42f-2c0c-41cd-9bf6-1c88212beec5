import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/providers/providers.dart';
import '../widgets/auth_header.dart';

/// Splash screen that handles app initialization
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _progressController;
  late Animation<double> _logoAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _logoController.forward();
    
    // Start app initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(appInitializationProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    _logoController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final initData = ref.watch(appInitializationProvider);
    final isAppReady = ref.watch(appReadyProvider);

    // Listen to initialization progress
    ref.listen<AppInitializationData>(appInitializationProvider, (previous, next) {
      if (next.progress > 0 && !_progressController.isAnimating) {
        _progressController.forward();
      }
      
      // Navigate when app is ready
      if (isAppReady && next.isInitialized) {
        _navigateToNextScreen();
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),
              
              // Animated logo
              AnimatedBuilder(
                animation: _logoAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoAnimation.value,
                    child: const AuthHeader(),
                  );
                },
              ),
              
              const Spacer(flex: 2),
              
              // Progress section
              AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _progressAnimation.value,
                    child: Column(
                      children: [
                        // Progress indicator
                        SizedBox(
                          width: 200.0,
                          child: LinearProgressIndicator(
                            value: initData.progress,
                            backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16.0),
                        
                        // Current step
                        Text(
                          initData.currentStep,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 8.0),
                        
                        // Progress percentage
                        Text(
                          '${(initData.progress * 100).toInt()}%',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              
              // Error handling
              if (initData.hasError) ...[
                const SizedBox(height: 24.0),
                
                Card(
                  color: Theme.of(context).colorScheme.errorContainer,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Theme.of(context).colorScheme.onErrorContainer,
                          size: 32.0,
                        ),
                        
                        const SizedBox(height: 8.0),
                        
                        Text(
                          'Gagal memuat aplikasi',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: Theme.of(context).colorScheme.onErrorContainer,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        
                        const SizedBox(height: 4.0),
                        
                        Text(
                          initData.error ?? 'Terjadi kesalahan yang tidak diketahui',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onErrorContainer,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 12.0),
                        
                        FilledButton(
                          onPressed: () {
                            ref.read(appInitializationProvider.notifier).retry();
                          },
                          style: FilledButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.onErrorContainer,
                            foregroundColor: Theme.of(context).colorScheme.errorContainer,
                          ),
                          child: const Text('Coba Lagi'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              
              const Spacer(),
              
              // App version
              Text(
                'v${AppConstants.appVersion}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToNextScreen() {
    // Add a small delay for better UX
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final isSignedIn = ref.read(isSignedInProvider);
        
        if (isSignedIn) {
          // Navigate to home page
          // TODO: Implement navigation to home page
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Welcome back! Navigating to home...'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // Navigate to login page
          // TODO: Implement navigation to login page
          Navigator.of(context).pushReplacementNamed('/login');
        }
      }
    });
  }
}
