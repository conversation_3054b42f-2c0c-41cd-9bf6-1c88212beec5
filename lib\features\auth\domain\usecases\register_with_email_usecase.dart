import '../entities/auth_result.dart';
import '../repositories/auth_repository.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/utils/either.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/utils/logger.dart';

/// Use case for registering new user with email and password
class RegisterWithEmailUseCase {
  const RegisterWithEmailUseCase(this.repository);

  final AuthRepository repository;

  /// Execute registration
  Future<Either<Failure, AuthResult>> call(RegistrationCredentials credentials) async {
    try {
      // Validate input
      final validationResult = _validateCredentials(credentials);
      if (validationResult != null) {
        return Left(validationResult);
      }

      // Perform registration
      final result = await repository.registerWithEmailAndPassword(credentials);
      
      AppLogger.info('User registration successful: ${credentials.email}');
      return Right(result);
    } catch (e, stackTrace) {
      AppLogger.error('Registration failed', e, stackTrace);
      
      if (e is Failure) {
        return Left(e);
      }
      
      return Left(
        UnknownFailure(
          message: 'Gagal mendaftar akun baru',
          details: e,
        ),
      );
    }
  }

  /// Validate registration credentials
  ValidationFailure? _validateCredentials(RegistrationCredentials credentials) {
    // Validate email
    if (credentials.email.isEmpty) {
      return const ValidationFailure(
        message: 'Email tidak boleh kosong',
        code: 'email_empty',
      );
    }

    if (!Validators.isValidEmail(credentials.email)) {
      return const ValidationFailure(
        message: 'Format email tidak valid',
        code: 'email_invalid',
      );
    }

    // Validate password
    if (credentials.password.isEmpty) {
      return const ValidationFailure(
        message: 'Password tidak boleh kosong',
        code: 'password_empty',
      );
    }

    if (credentials.password.length < 6) {
      return const ValidationFailure(
        message: 'Password minimal 6 karakter',
        code: 'password_too_short',
      );
    }

    // Validate display name
    if (credentials.displayName.isEmpty) {
      return const ValidationFailure(
        message: 'Nama tidak boleh kosong',
        code: 'display_name_empty',
      );
    }

    if (credentials.displayName.length < 2) {
      return const ValidationFailure(
        message: 'Nama minimal 2 karakter',
        code: 'display_name_too_short',
      );
    }

    if (credentials.displayName.length > 50) {
      return const ValidationFailure(
        message: 'Nama maksimal 50 karakter',
        code: 'display_name_too_long',
      );
    }

    // Validate phone number (optional)
    if (credentials.phoneNumber != null && credentials.phoneNumber!.isNotEmpty) {
      final phoneValidation = Validators.validatePhoneNumber(credentials.phoneNumber);
      if (phoneValidation != null) {
        return ValidationFailure(
          message: phoneValidation,
          code: 'phone_invalid',
        );
      }
    }

    return null;
  }
}
