import '../constants/app_constants.dart';

/// Utility class for input validation
class Validators {
  // Private constructor to prevent instantiation
  Validators._();

  /// Validates email format
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email tidak boleh kosong';
    }

    final emailRegExp = RegExp(AppConstants.emailRegex);
    if (!emailRegExp.hasMatch(value)) {
      return 'Format email tidak valid';
    }

    return null;
  }

  /// Checks if email format is valid (returns boolean)
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    final emailRegExp = RegExp(AppConstants.emailRegex);
    return emailRegExp.hasMatch(email);
  }

  /// Checks if password is strong (returns boolean)
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    final passwordRegExp = RegExp(AppConstants.passwordRegex);
    return passwordRegExp.hasMatch(password);
  }

  /// Checks if phone number format is valid (returns boolean)
  static bool isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return true; // Optional field

    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

    // Check Indonesian phone number format
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return false;
    }

    // Must start with 08, 62, or +62
    return digitsOnly.startsWith('08') ||
        digitsOnly.startsWith('62') ||
        phoneNumber.startsWith('+62');
  }

  /// Validates password strength
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password tidak boleh kosong';
    }

    if (value.length < 8) {
      return 'Password minimal 8 karakter';
    }

    final passwordRegExp = RegExp(AppConstants.passwordRegex);
    if (!passwordRegExp.hasMatch(value)) {
      return 'Password harus mengandung huruf besar, huruf kecil, angka, dan simbol';
    }

    return null;
  }

  /// Validates confirm password
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Konfirmasi password tidak boleh kosong';
    }

    if (value != password) {
      return 'Konfirmasi password tidak cocok';
    }

    return null;
  }

  /// Validates display name
  static String? validateDisplayName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nama tidak boleh kosong';
    }

    if (value.length < 2) {
      return 'Nama minimal 2 karakter';
    }

    if (value.length > 50) {
      return 'Nama maksimal 50 karakter';
    }

    return null;
  }

  /// Validates file name
  static String? validateFileName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nama file tidak boleh kosong';
    }

    if (value.length > 255) {
      return 'Nama file terlalu panjang';
    }

    // Check for invalid characters
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    if (invalidChars.hasMatch(value)) {
      return 'Nama file mengandung karakter tidak valid';
    }

    return null;
  }

  /// Validates file description
  static String? validateFileDescription(String? value) {
    if (value != null && value.length > 500) {
      return 'Deskripsi maksimal 500 karakter';
    }

    return null;
  }

  /// Validates category name
  static String? validateCategoryName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nama kategori tidak boleh kosong';
    }

    if (value.length < 2) {
      return 'Nama kategori minimal 2 karakter';
    }

    if (value.length > 50) {
      return 'Nama kategori maksimal 50 karakter';
    }

    return null;
  }

  /// Validates category description
  static String? validateCategoryDescription(String? value) {
    if (value != null && value.length > 200) {
      return 'Deskripsi kategori maksimal 200 karakter';
    }

    return null;
  }

  /// Validates phone number (Indonesian format)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone number is optional
    }

    // Remove all non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');

    // Check Indonesian phone number format
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return 'Nomor telepon tidak valid';
    }

    // Must start with 08, 62, or +62
    if (!digitsOnly.startsWith('08') &&
        !digitsOnly.startsWith('62') &&
        !value.startsWith('+62')) {
      return 'Nomor telepon harus dimulai dengan 08 atau 62';
    }

    return null;
  }

  /// Validates URL format
  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }

    final urlRegExp = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );

    if (!urlRegExp.hasMatch(value)) {
      return 'Format URL tidak valid';
    }

    return null;
  }

  /// Validates required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName tidak boleh kosong';
    }

    return null;
  }

  /// Validates minimum length
  static String? validateMinLength(
    String? value,
    int minLength,
    String fieldName,
  ) {
    if (value == null || value.length < minLength) {
      return '$fieldName minimal $minLength karakter';
    }

    return null;
  }

  /// Validates maximum length
  static String? validateMaxLength(
    String? value,
    int maxLength,
    String fieldName,
  ) {
    if (value != null && value.length > maxLength) {
      return '$fieldName maksimal $maxLength karakter';
    }

    return null;
  }

  /// Validates numeric input
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }

    if (double.tryParse(value) == null) {
      return '$fieldName harus berupa angka';
    }

    return null;
  }

  /// Validates positive number
  static String? validatePositiveNumber(String? value, String fieldName) {
    final numericValidation = validateNumeric(value, fieldName);
    if (numericValidation != null) {
      return numericValidation;
    }

    if (value != null && value.isNotEmpty) {
      final number = double.parse(value);
      if (number <= 0) {
        return '$fieldName harus lebih besar dari 0';
      }
    }

    return null;
  }

  /// Validates file size
  static String? validateFileSize(int fileSizeInBytes) {
    if (fileSizeInBytes > AppConstants.maxFileSize) {
      final maxSizeMB = AppConstants.maxFileSize / (1024 * 1024);
      return 'Ukuran file melebihi batas maksimal ${maxSizeMB.toInt()}MB';
    }

    return null;
  }

  /// Validates file type
  static String? validateFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    if (!AppConstants.allowedFileTypes.contains(extension)) {
      return 'Tipe file tidak diizinkan. Tipe yang diizinkan: ${AppConstants.allowedFileTypes.join(', ')}';
    }

    return null;
  }
}
