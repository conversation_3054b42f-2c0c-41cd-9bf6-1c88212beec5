import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/validators.dart';
import '../../../../shared/providers/providers.dart';
import 'google_sign_in_button.dart';
import 'password_reset_dialog.dart';

/// Login form widget with email/password and Google sign-in
class LoginForm extends ConsumerStatefulWidget {
  const LoginForm({super.key});

  @override
  ConsumerState<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends ConsumerState<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    
    // Load remember me preference
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rememberMe = ref.read(rememberMeEnabledProvider);
      setState(() {
        _rememberMe = rememberMe;
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleEmailLogin() {
    if (_formKey.currentState?.validate() ?? false) {
      // Update remember me preference
      ref.read(userPreferencesProvider.notifier).updateRememberMe(_rememberMe);
      
      // Perform login
      ref.read(authStateNotifierProvider.notifier).signInWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );
    }
  }

  void _handleGoogleLogin() {
    ref.read(authStateNotifierProvider.notifier).signInWithGoogle();
  }

  void _showPasswordResetDialog() {
    showDialog(
      context: context,
      builder: (context) => PasswordResetDialog(
        initialEmail: _emailController.text.trim(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(isAuthLoadingProvider);
    
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email field
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            enabled: !isLoading,
            decoration: const InputDecoration(
              labelText: 'Email',
              hintText: 'Masukkan email Anda',
              prefixIcon: Icon(Icons.email_outlined),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Email tidak boleh kosong';
              }
              if (!Validators.isValidEmail(value)) {
                return 'Format email tidak valid';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16.0),
          
          // Password field
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            textInputAction: TextInputAction.done,
            enabled: !isLoading,
            decoration: InputDecoration(
              labelText: 'Password',
              hintText: 'Masukkan password Anda',
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Password tidak boleh kosong';
              }
              if (value.length < 6) {
                return 'Password minimal 6 karakter';
              }
              return null;
            },
            onFieldSubmitted: (_) => _handleEmailLogin(),
          ),
          
          const SizedBox(height: 16.0),
          
          // Remember me and forgot password row
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: isLoading ? null : (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
              ),
              const Text('Ingat saya'),
              
              const Spacer(),
              
              TextButton(
                onPressed: isLoading ? null : _showPasswordResetDialog,
                child: const Text('Lupa password?'),
              ),
            ],
          ),
          
          const SizedBox(height: 24.0),
          
          // Login button
          FilledButton(
            onPressed: isLoading ? null : _handleEmailLogin,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: isLoading
                  ? const SizedBox(
                      height: 20.0,
                      width: 20.0,
                      child: CircularProgressIndicator(strokeWidth: 2.0),
                    )
                  : const Text('Masuk'),
            ),
          ),
          
          const SizedBox(height: 16.0),
          
          // Divider
          Row(
            children: [
              const Expanded(child: Divider()),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  'atau',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              const Expanded(child: Divider()),
            ],
          ),
          
          const SizedBox(height: 16.0),
          
          // Google sign-in button
          GoogleSignInButton(
            onPressed: isLoading ? null : _handleGoogleLogin,
            isLoading: isLoading,
          ),
        ],
      ),
    );
  }
}
