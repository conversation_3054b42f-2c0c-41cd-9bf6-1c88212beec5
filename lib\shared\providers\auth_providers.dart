import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/services/auth_service.dart';
import '../../features/auth/data/datasources/auth_remote_datasource.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import '../../features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import '../../features/auth/domain/usecases/register_with_email_usecase.dart';
import '../../features/auth/domain/usecases/sign_out_usecase.dart';
import '../../features/auth/domain/usecases/send_password_reset_usecase.dart';
import '../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../../features/auth/domain/entities/user_entity.dart';
import '../../features/auth/domain/entities/auth_result.dart';

/// Provider for AuthService instance
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService.instance;
});

/// Provider for Firestore instance
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Provider for AuthRemoteDataSource
final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  final authService = ref.watch(authServiceProvider);
  final firestore = ref.watch(firestoreProvider);

  return AuthRemoteDataSourceImpl(
    authService: authService,
    firestore: firestore,
  );
});

/// Provider for AuthRepository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);

  return AuthRepositoryImpl(remoteDataSource: remoteDataSource);
});

/// Provider for SignInWithEmailUseCase
final signInWithEmailUseCaseProvider = Provider<SignInWithEmailUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return SignInWithEmailUseCase(repository);
});

/// Provider for SignInWithGoogleUseCase
final signInWithGoogleUseCaseProvider = Provider<SignInWithGoogleUseCase>((
  ref,
) {
  final repository = ref.watch(authRepositoryProvider);

  return SignInWithGoogleUseCase(repository);
});

/// Provider for RegisterWithEmailUseCase
final registerWithEmailUseCaseProvider = Provider<RegisterWithEmailUseCase>((
  ref,
) {
  final repository = ref.watch(authRepositoryProvider);

  return RegisterWithEmailUseCase(repository);
});

/// Provider for SignOutUseCase
final signOutUseCaseProvider = Provider<SignOutUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return SignOutUseCase(repository);
});

/// Provider for SendPasswordResetUseCase
final sendPasswordResetUseCaseProvider = Provider<SendPasswordResetUseCase>((
  ref,
) {
  final repository = ref.watch(authRepositoryProvider);

  return SendPasswordResetUseCase(repository);
});

/// Provider for GetCurrentUserUseCase
final getCurrentUserUseCaseProvider = Provider<GetCurrentUserUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return GetCurrentUserUseCase(repository);
});

/// Provider for current user stream
final authStateProvider = StreamProvider<UserEntity?>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return repository.authStateChanges;
});

/// Provider for current user (synchronous)
final currentUserProvider = Provider<UserEntity?>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return repository.currentUser;
});

/// Provider for authentication status
final isSignedInProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) => user != null,
    loading: () => false,
    error: (_, __) => false,
  );
});

/// Provider for checking if user is admin
final isAdminProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);

  return user?.role == UserRole.admin;
});

/// Provider for user display name
final userDisplayNameProvider = Provider<String>((ref) {
  final user = ref.watch(currentUserProvider);

  return user?.displayName ?? 'Guest';
});

/// Provider for user email
final userEmailProvider = Provider<String>((ref) {
  final user = ref.watch(currentUserProvider);

  return user?.email ?? '';
});

/// Provider for checking if email is verified
final isEmailVerifiedProvider = Provider<bool>((ref) {
  final repository = ref.watch(authRepositoryProvider);

  return repository.isEmailVerified;
});

/// Provider for checking if password change is required
final passwordChangeRequiredProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);

  return user?.passwordChangeRequired ?? false;
});
