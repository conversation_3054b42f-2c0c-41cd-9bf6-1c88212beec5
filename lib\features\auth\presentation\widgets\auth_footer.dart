import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Authentication footer with additional information and links
class AuthFooter extends StatelessWidget {
  const AuthFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Help text
        Card(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24.0,
                ),
                
                const SizedBox(height: 8.0),
                
                Text(
                  'Butuh Bantuan?',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                
                const SizedBox(height: 4.0),
                
                Text(
                  'Hubungi administrator sistem jika <PERSON>a mengal<PERSON> kesulitan dalam mengakses akun.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 12.0),
                
                // Contact buttons
                Wrap(
                  spacing: 8.0,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        // TODO: Implement email contact
                        _showContactInfo(context, 'Email', '<EMAIL>');
                      },
                      icon: const Icon(Icons.email_outlined, size: 16.0),
                      label: const Text('Email'),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                      ),
                    ),
                    
                    TextButton.icon(
                      onPressed: () {
                        // TODO: Implement phone contact
                        _showContactInfo(context, 'Telepon', '(021) 1234-5678');
                      },
                      icon: const Icon(Icons.phone_outlined, size: 16.0),
                      label: const Text('Telepon'),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 24.0),
        
        // App version and copyright
        Column(
          children: [
            Text(
              '${AppConstants.appName} v${AppConstants.appVersion}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            
            const SizedBox(height: 4.0),
            
            Text(
              '© 2024 Balai Penyuluhan Pertanian',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            
            const SizedBox(height: 8.0),
            
            // Additional links
            Wrap(
              spacing: 16.0,
              children: [
                TextButton(
                  onPressed: () {
                    _showInfoDialog(context, 'Kebijakan Privasi', 
                      'Informasi tentang bagaimana kami melindungi dan mengelola data pribadi Anda.');
                  },
                  child: Text(
                    'Kebijakan Privasi',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                
                TextButton(
                  onPressed: () {
                    _showInfoDialog(context, 'Syarat & Ketentuan', 
                      'Syarat dan ketentuan penggunaan sistem manajemen dokumen BAPELTAN.');
                  },
                  child: Text(
                    'Syarat & Ketentuan',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  void _showContactInfo(BuildContext context, String type, String info) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Kontak $type'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              type == 'Email' ? Icons.email : Icons.phone,
              size: 48.0,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16.0),
            Text(
              info,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Silakan hubungi administrator melalui $type di atas untuk bantuan teknis.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }
}
