import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';
import '../utils/logger.dart';
import 'firebase_service.dart';

/// Authentication service for handling user authentication
class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  late final FirebaseAuth _auth;
  late final GoogleSignIn _googleSignIn;
  late final FirebaseFirestore _firestore;

  bool _initialized = false;

  /// Initialize the authentication service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _auth = FirebaseService.instance.auth;
      _firestore = FirebaseService.instance.firestore;

      _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

      _initialized = true;
      AppLogger.info('AuthService initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to initialize AuthService', e, stackTrace);
      rethrow;
    }
  }

  /// Get current user
  User? get currentUser => _auth.currentUser;

  /// Get current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Check if user is signed in
  bool get isSignedIn => currentUser != null;

  /// Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      AppLogger.auth('Attempting email/password sign in', {'email': email});

      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _updateLastLogin(credential.user!.uid);
        AppLogger.auth('Email/password sign in successful', {
          'uid': credential.user!.uid,
          'email': credential.user!.email,
        });
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during email/password sign in', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error(
        'Unexpected error during email/password sign in',
        e,
        stackTrace,
      );
      throw AuthException(
        message: 'Terjadi kesalahan saat masuk. Silakan coba lagi.',
        code: 'unknown_error',
        details: e,
      );
    }
  }

  /// Create user with email and password
  Future<UserCredential> createUserWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      AppLogger.auth('Attempting user creation', {'email': email});

      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        AppLogger.auth('User creation successful', {
          'uid': credential.user!.uid,
          'email': credential.user!.email,
        });
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during user creation', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during user creation', e, stackTrace);
      throw AuthException(
        message: 'Terjadi kesalahan saat membuat akun. Silakan coba lagi.',
        code: 'unknown_error',
        details: e,
      );
    }
  }

  /// Sign in with Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      AppLogger.auth('Attempting Google sign in');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        throw AuthException(
          message: 'Proses masuk dengan Google dibatalkan',
          code: 'sign_in_cancelled',
        );
      }

      // Check if email is approved
      await _checkEmailApproved(googleUser.email);

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _updateLastLogin(userCredential.user!.uid);
        AppLogger.auth('Google sign in successful', {
          'uid': userCredential.user!.uid,
          'email': userCredential.user!.email,
        });
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during Google sign in', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during Google sign in', e, stackTrace);
      if (e is AuthException) rethrow;
      throw AuthException(
        message:
            'Terjadi kesalahan saat masuk dengan Google. Silakan coba lagi.',
        code: 'google_sign_in_error',
        details: e,
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      AppLogger.auth('Attempting sign out');

      // Sign out from Google if signed in
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }

      // Sign out from Firebase
      await _auth.signOut();

      AppLogger.auth('Sign out successful');
    } catch (e, stackTrace) {
      AppLogger.error('Error during sign out', e, stackTrace);
      throw AuthException(
        message: 'Terjadi kesalahan saat keluar. Silakan coba lagi.',
        code: 'sign_out_error',
        details: e,
      );
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      AppLogger.auth('Sending password reset email', {'email': email});

      await _auth.sendPasswordResetEmail(email: email);

      AppLogger.auth('Password reset email sent successfully', {
        'email': email,
      });
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during password reset', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during password reset', e, stackTrace);
      throw AuthException(
        message: 'Terjadi kesalahan saat mengirim email reset password.',
        code: 'password_reset_error',
        details: e,
      );
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      AppLogger.auth('Updating password', {'uid': user.uid});

      await user.updatePassword(newPassword);

      AppLogger.auth('Password updated successfully', {'uid': user.uid});
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during password update', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during password update', e, stackTrace);
      throw AuthException(
        message: 'Terjadi kesalahan saat mengubah password.',
        code: 'password_update_error',
        details: e,
      );
    }
  }

  /// Update user profile
  Future<void> updateProfile({String? displayName, String? photoURL}) async {
    try {
      final user = currentUser;
      if (user == null) {
        throw AuthException(
          message: 'Pengguna tidak ditemukan',
          code: 'user_not_found',
        );
      }

      AppLogger.auth('Updating user profile', {
        'uid': user.uid,
        'displayName': displayName,
        'photoURL': photoURL,
      });

      await user.updateDisplayName(displayName);
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }

      AppLogger.auth('User profile updated successfully', {'uid': user.uid});
    } on FirebaseAuthException catch (e) {
      AppLogger.error('Firebase auth error during profile update', e);
      throw _handleFirebaseAuthException(e);
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during profile update', e, stackTrace);
      throw AuthException(
        message: 'Terjadi kesalahan saat mengubah profil.',
        code: 'profile_update_error',
        details: e,
      );
    }
  }

  /// Reload current user
  Future<void> reloadUser() async {
    try {
      final user = currentUser;
      if (user != null) {
        await user.reload();
      }
    } catch (e, stackTrace) {
      AppLogger.error('Error reloading user', e, stackTrace);
    }
  }

  /// Check if email is approved for registration
  Future<void> _checkEmailApproved(String email) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.approvedEmailsCollection)
          .doc(email)
          .get();

      if (!doc.exists) {
        throw AuthException(
          message:
              'Akun belum terdaftar. Hubungi administrator untuk membuat akun.',
          code: 'email_not_approved',
        );
      }

      final data = doc.data()!;
      if (data['status'] != 'pending' && data['status'] != 'activated') {
        throw AuthException(
          message: 'Akun tidak aktif. Hubungi administrator.',
          code: 'account_inactive',
        );
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException(
        message: 'Terjadi kesalahan saat memverifikasi akun.',
        code: 'verification_error',
        details: e,
      );
    }
  }

  /// Update last login timestamp
  Future<void> _updateLastLogin(String uid) async {
    try {
      await _firestore.collection(AppConstants.usersCollection).doc(uid).update(
        {'lastLogin': FieldValue.serverTimestamp()},
      );
    } catch (e) {
      AppLogger.warning('Failed to update last login', e);
      // Don't throw error as this is not critical
    }
  }

  /// Handle Firebase Auth exceptions
  AuthException _handleFirebaseAuthException(FirebaseAuthException e) {
    String message;

    switch (e.code) {
      case 'user-not-found':
        message = 'Pengguna tidak ditemukan.';
        break;
      case 'wrong-password':
        message = 'Password salah.';
        break;
      case 'invalid-email':
        message = 'Format email tidak valid.';
        break;
      case 'user-disabled':
        message = 'Akun telah dinonaktifkan.';
        break;
      case 'too-many-requests':
        message = 'Terlalu banyak percobaan. Silakan coba lagi nanti.';
        break;
      case 'operation-not-allowed':
        message = 'Operasi tidak diizinkan.';
        break;
      case 'weak-password':
        message = 'Password terlalu lemah.';
        break;
      case 'email-already-in-use':
        message = 'Email sudah digunakan.';
        break;
      case 'requires-recent-login':
        message = 'Silakan masuk ulang untuk melanjutkan.';
        break;
      case 'network-request-failed':
        message = 'Tidak dapat terhubung ke server. Periksa koneksi internet.';
        break;
      default:
        message = e.message ?? 'Terjadi kesalahan autentikasi.';
    }

    return AuthException(message: message, code: e.code, details: e);
  }

  /// Dispose resources
  void dispose() {
    // Clean up resources if needed
    AppLogger.info('AuthService disposed');
  }
}
