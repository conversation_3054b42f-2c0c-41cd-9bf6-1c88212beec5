import '../../../../core/base/usecase.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing out
class SignOutUseCase implements UseCase<void, NoParams> {
  const SignOutUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    try {
      await _repository.signOut();
      return const Right(null);
    } catch (e) {
      return Left(
        AuthFailure(
          message: '<PERSON><PERSON> keluar dari aplikasi',
          details: e.toString(),
        ),
      );
    }
  }
}
