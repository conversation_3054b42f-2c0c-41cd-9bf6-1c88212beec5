import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../../features/auth/domain/entities/user_entity.dart';
import 'auth_providers.dart';

/// User preferences state
class UserPreferencesState {
  const UserPreferencesState({
    this.theme = 'light',
    this.language = 'id',
    this.emailNotifications = true,
    this.rememberMe = false,
  });

  final String theme;
  final String language;
  final bool emailNotifications;
  final bool rememberMe;

  UserPreferencesState copyWith({
    String? theme,
    String? language,
    bool? emailNotifications,
    bool? rememberMe,
  }) {
    return UserPreferencesState(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'theme': theme,
      'language': language,
      'emailNotifications': emailNotifications,
      'rememberMe': rememberMe,
    };
  }

  factory UserPreferencesState.fromJson(Map<String, dynamic> json) {
    return UserPreferencesState(
      theme: json['theme'] as String? ?? 'light',
      language: json['language'] as String? ?? 'id',
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      rememberMe: json['rememberMe'] as bool? ?? false,
    );
  }
}

/// User preferences notifier
class UserPreferencesNotifier extends StateNotifier<UserPreferencesState> {
  UserPreferencesNotifier() : super(const UserPreferencesState()) {
    _loadPreferences();
  }

  /// Load preferences from SharedPreferences
  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final theme = prefs.getString(AppConstants.themePrefsKey) ?? 'light';
      final language = prefs.getString(AppConstants.languagePrefsKey) ?? 'id';
      final emailNotifications = prefs.getBool('emailNotifications') ?? true;
      final rememberMe = prefs.getBool(AppConstants.rememberMeKey) ?? false;

      state = UserPreferencesState(
        theme: theme,
        language: language,
        emailNotifications: emailNotifications,
        rememberMe: rememberMe,
      );

      AppLogger.info('User preferences loaded successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to load user preferences', e, stackTrace);
    }
  }

  /// Save preferences to SharedPreferences
  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString(AppConstants.themePrefsKey, state.theme);
      await prefs.setString(AppConstants.languagePrefsKey, state.language);
      await prefs.setBool('emailNotifications', state.emailNotifications);
      await prefs.setBool(AppConstants.rememberMeKey, state.rememberMe);

      AppLogger.info('User preferences saved successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to save user preferences', e, stackTrace);
    }
  }

  /// Update theme preference
  Future<void> updateTheme(String theme) async {
    state = state.copyWith(theme: theme);
    await _savePreferences();
  }

  /// Update language preference
  Future<void> updateLanguage(String language) async {
    state = state.copyWith(language: language);
    await _savePreferences();
  }

  /// Update email notifications preference
  Future<void> updateEmailNotifications(bool enabled) async {
    state = state.copyWith(emailNotifications: enabled);
    await _savePreferences();
  }

  /// Update remember me preference
  Future<void> updateRememberMe(bool enabled) async {
    state = state.copyWith(rememberMe: enabled);
    await _savePreferences();
  }

  /// Reset preferences to default
  Future<void> resetToDefaults() async {
    state = const UserPreferencesState();
    await _savePreferences();
  }

  /// Sync preferences with user entity
  Future<void> syncWithUserEntity(UserEntity user) async {
    if (user.preferences != null) {
      state = UserPreferencesState(
        theme: user.preferences!.theme,
        language: user.preferences!.language,
        emailNotifications: user.preferences!.emailNotifications,
        rememberMe: state.rememberMe, // Keep local remember me setting
      );
      await _savePreferences();
    }
  }
}

/// Provider for SharedPreferences instance
final sharedPreferencesProvider = FutureProvider<SharedPreferences>((ref) async {
  return await SharedPreferences.getInstance();
});

/// Provider for UserPreferencesNotifier
final userPreferencesProvider = StateNotifierProvider<UserPreferencesNotifier, UserPreferencesState>((ref) {
  return UserPreferencesNotifier();
});

/// Provider for current theme
final currentThemeProvider = Provider<String>((ref) {
  final preferences = ref.watch(userPreferencesProvider);
  return preferences.theme;
});

/// Provider for current language
final currentLanguageProvider = Provider<String>((ref) {
  final preferences = ref.watch(userPreferencesProvider);
  return preferences.language;
});

/// Provider for email notifications setting
final emailNotificationsEnabledProvider = Provider<bool>((ref) {
  final preferences = ref.watch(userPreferencesProvider);
  return preferences.emailNotifications;
});

/// Provider for remember me setting
final rememberMeEnabledProvider = Provider<bool>((ref) {
  final preferences = ref.watch(userPreferencesProvider);
  return preferences.rememberMe;
});

/// Provider for checking if dark theme is enabled
final isDarkThemeProvider = Provider<bool>((ref) {
  final theme = ref.watch(currentThemeProvider);
  return theme == 'dark';
});

/// Provider for checking if system theme is enabled
final isSystemThemeProvider = Provider<bool>((ref) {
  final theme = ref.watch(currentThemeProvider);
  return theme == 'system';
});

/// Provider for checking if Indonesian language is selected
final isIndonesianLanguageProvider = Provider<bool>((ref) {
  final language = ref.watch(currentLanguageProvider);
  return language == 'id';
});

/// Provider for checking if English language is selected
final isEnglishLanguageProvider = Provider<bool>((ref) {
  final language = ref.watch(currentLanguageProvider);
  return language == 'en';
});
