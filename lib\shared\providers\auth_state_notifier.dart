import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/base/base_state.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../features/auth/domain/entities/auth_result.dart';
import '../../features/auth/domain/entities/user_entity.dart';
import '../../features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import '../../features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import '../../features/auth/domain/usecases/register_with_email_usecase.dart';
import '../../features/auth/domain/usecases/sign_out_usecase.dart';
import '../../features/auth/domain/usecases/send_password_reset_usecase.dart';
import 'auth_providers.dart';

/// Authentication state notifier for managing auth operations
class AuthStateNotifier extends StateNotifier<BaseState> {
  AuthStateNotifier({
    required this.signInWithEmailUseCase,
    required this.signInWithGoogleUseCase,
    required this.registerWithEmailUseCase,
    required this.signOutUseCase,
    required this.sendPasswordResetUseCase,
  }) : super(const InitialState());

  final SignInWithEmailUseCase signInWithEmailUseCase;
  final SignInWithGoogleUseCase signInWithGoogleUseCase;
  final RegisterWithEmailUseCase registerWithEmailUseCase;
  final SignOutUseCase signOutUseCase;
  final SendPasswordResetUseCase sendPasswordResetUseCase;

  /// Sign in with email and password
  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = const LoadingState(message: 'Signing in...');

    try {
      final credentials = SignInCredentials(email: email, password: password);
      final result = await signInWithEmailUseCase.call(credentials);

      result.fold(
        (failure) {
          AppLogger.error('Sign in failed', failure);
          state = ErrorState(failure: failure);
        },
        (authResult) {
          AppLogger.success('Sign in successful');
          state = SuccessState<AuthResult>(
            data: authResult,
            message: 'Successfully signed in',
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during sign in', e, stackTrace);
      state = ErrorState(
        failure: UnknownFailure(
          message: 'An unexpected error occurred during sign in',
          details: e,
        ),
      );
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    state = const LoadingState(message: 'Signing in with Google...');

    try {
      final result = await signInWithGoogleUseCase.call();

      result.fold(
        (failure) {
          AppLogger.error('Google sign in failed', failure);
          state = ErrorState(failure: failure);
        },
        (authResult) {
          AppLogger.success('Google sign in successful');
          state = SuccessState<AuthResult>(
            data: authResult,
            message: 'Successfully signed in with Google',
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during Google sign in', e, stackTrace);
      state = ErrorState(
        failure: UnknownFailure(
          message: 'An unexpected error occurred during Google sign in',
          details: e,
        ),
      );
    }
  }

  /// Register new user with email and password
  Future<void> registerWithEmail({
    required String email,
    required String password,
    required String displayName,
    String? phoneNumber,
  }) async {
    state = const LoadingState(message: 'Creating account...');

    try {
      final credentials = RegistrationCredentials(
        email: email,
        password: password,
        displayName: displayName,
        phoneNumber: phoneNumber,
      );
      final result = await registerWithEmailUseCase.call(credentials);

      result.fold(
        (Failure failure) {
          AppLogger.error('Registration failed', failure);
          state = ErrorState(failure: failure);
        },
        (AuthResult authResult) {
          AppLogger.success('Registration successful');
          state = SuccessState<AuthResult>(
            data: authResult,
            message:
                'Account created successfully. Please check your email for verification.',
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during registration', e, stackTrace);
      state = ErrorState(
        failure: UnknownFailure(
          message: 'An unexpected error occurred during registration',
          details: e,
        ),
      );
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    state = const LoadingState(message: 'Signing out...');

    try {
      final result = await signOutUseCase.call();

      result.fold(
        (failure) {
          AppLogger.error('Sign out failed', failure);
          state = ErrorState(failure: failure);
        },
        (_) {
          AppLogger.success('Sign out successful');
          state = const SuccessState<void>(
            data: null,
            message: 'Successfully signed out',
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error during sign out', e, stackTrace);
      state = ErrorState(
        failure: UnknownFailure(
          message: 'An unexpected error occurred during sign out',
          details: e,
        ),
      );
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    state = const LoadingState(message: 'Sending password reset email...');

    try {
      final request = PasswordResetRequest(email: email);
      final result = await sendPasswordResetUseCase.call(request);

      result.fold(
        (failure) {
          AppLogger.error('Password reset email failed', failure);
          state = ErrorState(failure: failure);
        },
        (_) {
          AppLogger.success('Password reset email sent');
          state = const SuccessState<void>(
            data: null,
            message: 'Password reset email sent successfully',
          );
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'Unexpected error sending password reset email',
        e,
        stackTrace,
      );
      state = ErrorState(
        failure: UnknownFailure(
          message:
              'An unexpected error occurred while sending password reset email',
          details: e,
        ),
      );
    }
  }

  /// Reset state to initial
  void resetState() {
    state = const InitialState();
  }

  /// Clear any error state
  void clearError() {
    if (state.hasError) {
      state = const InitialState();
    }
  }
}

/// Provider for AuthStateNotifier
final authStateNotifierProvider =
    StateNotifierProvider<AuthStateNotifier, BaseState>((ref) {
      final signInWithEmailUseCase = ref.watch(signInWithEmailUseCaseProvider);
      final signInWithGoogleUseCase = ref.watch(
        signInWithGoogleUseCaseProvider,
      );
      final registerWithEmailUseCase = ref.watch(
        registerWithEmailUseCaseProvider,
      );
      final signOutUseCase = ref.watch(signOutUseCaseProvider);
      final sendPasswordResetUseCase = ref.watch(
        sendPasswordResetUseCaseProvider,
      );

      return AuthStateNotifier(
        signInWithEmailUseCase: signInWithEmailUseCase,
        signInWithGoogleUseCase: signInWithGoogleUseCase,
        registerWithEmailUseCase: registerWithEmailUseCase,
        signOutUseCase: signOutUseCase,
        sendPasswordResetUseCase: sendPasswordResetUseCase,
      );
    });

/// Provider for checking if auth operation is loading
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateNotifierProvider);
  return authState.isLoading;
});

/// Provider for getting auth error message
final authErrorMessageProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateNotifierProvider);
  return authState.getErrorMessage();
});

/// Provider for checking if auth operation was successful
final isAuthSuccessProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateNotifierProvider);
  return authState is SuccessState;
});

/// Provider for getting auth success message
final authSuccessMessageProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateNotifierProvider);
  if (authState is SuccessState) {
    return authState.message;
  }
  return null;
});
