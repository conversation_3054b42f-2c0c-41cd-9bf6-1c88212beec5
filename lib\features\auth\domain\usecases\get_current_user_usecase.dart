import '../../../../core/base/usecase.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for getting current authenticated user
class GetCurrentUserUseCase implements UseCase<UserEntity?, NoParams> {
  const GetCurrentUserUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, UserEntity?>> call(NoParams params) async {
    try {
      final user = _repository.currentUser;
      return Right(user);
    } catch (e) {
      return Left(
        AuthFailure(
          message: 'Gagal mendapatkan data pengguna',
          details: e.toString(),
        ),
      );
    }
  }
}

/// Use case for refreshing current user data
class RefreshCurrentUserUseCase implements UseCase<UserEntity?, NoParams> {
  const RefreshCurrentUserUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Either<Failure, UserEntity?>> call(NoParams params) async {
    try {
      final user = await _repository.refreshCurrentUser();
      return Right(user);
    } catch (e) {
      return Left(
        AuthFailure(
          message: 'Gagal memperbarui data pengguna',
          details: e.toString(),
        ),
      );
    }
  }
}
