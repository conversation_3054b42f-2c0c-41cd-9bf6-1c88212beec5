import '../../domain/entities/user_entity.dart';

/// User model for data layer
class UserModel {
  const UserModel({
    required this.uid,
    required this.email,
    required this.displayName,
    required this.role,
    required this.status,
    required this.authMethod,
    required this.passwordChangeRequired,
    required this.createdAt,
    this.createdBy,
    this.lastLogin,
    this.department,
    this.profilePicture,
    this.preferences,
  });

  final String uid;
  final String email;
  final String displayName;
  final UserRole role;
  final UserStatus status;
  final AuthMethod authMethod;
  final bool passwordChangeRequired;
  final DateTime createdAt;
  final String? createdBy;
  final DateTime? lastLogin;
  final String? department;
  final String? profilePicture;
  final UserPreferencesModel? preferences;

  /// Create UserModel from Firestore document
  factory UserModel.fromFirestore(Map<String, dynamic> data) {
    return UserModel(
      uid: data['uid'] as String,
      email: data['email'] as String,
      displayName: data['displayName'] as String? ?? '',
      role: UserRole.fromString(data['role'] as String? ?? 'user'),
      status: UserStatus.fromString(data['status'] as String? ?? 'active'),
      authMethod: AuthMethod.fromString(data['authMethod'] as String? ?? 'email'),
      passwordChangeRequired: data['passwordChangeRequired'] as bool? ?? false,
      createdAt: _parseDateTime(data['createdAt']),
      createdBy: data['createdBy'] as String?,
      lastLogin: _parseDateTime(data['lastLogin']),
      department: data['department'] as String?,
      profilePicture: data['profilePicture'] as String?,
      preferences: data['preferences'] != null
          ? UserPreferencesModel.fromMap(data['preferences'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Convert UserModel to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'role': role.name,
      'status': status.name,
      'authMethod': authMethod.name,
      'passwordChangeRequired': passwordChangeRequired,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'lastLogin': lastLogin,
      'department': department,
      'profilePicture': profilePicture,
      'preferences': preferences?.toMap(),
    };
  }

  /// Create UserModel from UserEntity
  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      uid: entity.uid,
      email: entity.email,
      displayName: entity.displayName,
      role: entity.role,
      status: entity.status,
      authMethod: entity.authMethod,
      passwordChangeRequired: entity.passwordChangeRequired,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      lastLogin: entity.lastLogin,
      department: entity.department,
      profilePicture: entity.profilePicture,
      preferences: entity.preferences != null
          ? UserPreferencesModel.fromEntity(entity.preferences!)
          : null,
    );
  }

  /// Convert to UserEntity
  UserEntity toEntity() {
    return UserEntity(
      uid: uid,
      email: email,
      displayName: displayName,
      role: role,
      status: status,
      authMethod: authMethod,
      passwordChangeRequired: passwordChangeRequired,
      createdAt: createdAt,
      createdBy: createdBy,
      lastLogin: lastLogin,
      department: department,
      profilePicture: profilePicture,
      preferences: preferences?.toEntity(),
    );
  }

  /// Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is DateTime) return value;
    if (value is String) return DateTime.parse(value);
    // Handle Firestore Timestamp
    if (value.runtimeType.toString() == 'Timestamp') {
      return (value as dynamic).toDate() as DateTime;
    }
    return DateTime.now();
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    UserRole? role,
    UserStatus? status,
    AuthMethod? authMethod,
    bool? passwordChangeRequired,
    DateTime? createdAt,
    String? createdBy,
    DateTime? lastLogin,
    String? department,
    String? profilePicture,
    UserPreferencesModel? preferences,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      role: role ?? this.role,
      status: status ?? this.status,
      authMethod: authMethod ?? this.authMethod,
      passwordChangeRequired: passwordChangeRequired ?? this.passwordChangeRequired,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      lastLogin: lastLogin ?? this.lastLogin,
      department: department ?? this.department,
      profilePicture: profilePicture ?? this.profilePicture,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// User preferences model for data layer
class UserPreferencesModel {
  const UserPreferencesModel({
    this.theme = 'light',
    this.language = 'id',
    this.emailNotifications = true,
  });

  final String theme;
  final String language;
  final bool emailNotifications;

  /// Create UserPreferencesModel from map
  factory UserPreferencesModel.fromMap(Map<String, dynamic> map) {
    return UserPreferencesModel(
      theme: map['theme'] as String? ?? 'light',
      language: map['language'] as String? ?? 'id',
      emailNotifications: map['emailNotifications'] as bool? ?? true,
    );
  }

  /// Convert UserPreferencesModel to map
  Map<String, dynamic> toMap() {
    return {
      'theme': theme,
      'language': language,
      'emailNotifications': emailNotifications,
    };
  }

  /// Create UserPreferencesModel from UserPreferences entity
  factory UserPreferencesModel.fromEntity(UserPreferences entity) {
    return UserPreferencesModel(
      theme: entity.theme,
      language: entity.language,
      emailNotifications: entity.emailNotifications,
    );
  }

  /// Convert to UserPreferences entity
  UserPreferences toEntity() {
    return UserPreferences(
      theme: theme,
      language: language,
      emailNotifications: emailNotifications,
    );
  }

  UserPreferencesModel copyWith({
    String? theme,
    String? language,
    bool? emailNotifications,
  }) {
    return UserPreferencesModel(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      emailNotifications: emailNotifications ?? this.emailNotifications,
    );
  }
}
