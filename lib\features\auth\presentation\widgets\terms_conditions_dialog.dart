import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Dialog for displaying terms and conditions
class TermsConditionsDialog extends StatelessWidget {
  const TermsConditionsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12.0),
                  topRight: Radius.circular(12.0),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.description_outlined,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    size: 28.0,
                  ),
                  const SizedBox(width: 12.0),
                  Expanded(
                    child: Text(
                      '<PERSON>yarat dan <PERSON>',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Syarat dan Ketentuan Penggunaan\n${AppConstants.appName}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 16.0),
                    
                    Text(
                      'Terakhir diperbarui: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    
                    const SizedBox(height: 24.0),
                    
                    _buildSection(
                      context,
                      '1. Penerimaan Syarat',
                      'Dengan menggunakan aplikasi ${AppConstants.appName}, Anda menyetujui untuk terikat oleh syarat dan ketentuan ini. Jika Anda tidak menyetujui syarat ini, mohon untuk tidak menggunakan aplikasi.',
                    ),
                    
                    _buildSection(
                      context,
                      '2. Penggunaan Aplikasi',
                      'Aplikasi ini diperuntukkan untuk keperluan resmi Balai Penyuluhan Pertanian. Pengguna bertanggung jawab untuk menjaga kerahasiaan akun dan password mereka.',
                    ),
                    
                    _buildSection(
                      context,
                      '3. Privasi dan Keamanan Data',
                      'Kami berkomitmen untuk melindungi privasi dan keamanan data Anda. Data yang dikumpulkan akan digunakan sesuai dengan kebijakan privasi yang berlaku.',
                    ),
                    
                    _buildSection(
                      context,
                      '4. Kewajiban Pengguna',
                      '• Menggunakan aplikasi sesuai dengan tujuan yang dimaksudkan\n• Tidak menyalahgunakan sistem atau data\n• Melaporkan masalah keamanan yang ditemukan\n• Menjaga kerahasiaan informasi sensitif',
                    ),
                    
                    _buildSection(
                      context,
                      '5. Batasan Tanggung Jawab',
                      'Balai Penyuluhan Pertanian tidak bertanggung jawab atas kerugian yang timbul akibat penggunaan aplikasi ini, kecuali yang diwajibkan oleh hukum.',
                    ),
                    
                    _buildSection(
                      context,
                      '6. Perubahan Syarat',
                      'Syarat dan ketentuan ini dapat berubah sewaktu-waktu. Pengguna akan diberitahu mengenai perubahan penting melalui aplikasi.',
                    ),
                    
                    _buildSection(
                      context,
                      '7. Kontak',
                      'Untuk pertanyaan mengenai syarat dan ketentuan ini, silakan hubungi administrator sistem melalui email atau telepon yang tersedia.',
                    ),
                    
                    const SizedBox(height: 24.0),
                    
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 12.0),
                          Expanded(
                            child: Text(
                              'Dengan mendaftar, Anda menyetujui semua syarat dan ketentuan di atas.',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Footer
            Container(
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Tutup'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
