---
description: 'Enforce the consistent use of either backticks, double, or single quotes.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/quotes** for documentation.

This rule extends the base [`eslint/quotes`](https://eslint.org/docs/rules/quotes) rule.
It adds support for TypeScript features which allow quoted names, but not backtick quoted names.
