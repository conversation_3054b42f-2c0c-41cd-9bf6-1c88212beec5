/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'BAPELTAN SIMDOC';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'Admin-Controlled File Management System';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String filesCollection = 'files';
  static const String categoriesCollection = 'categories';
  static const String activitiesCollection = 'activities';
  static const String approvedEmailsCollection = 'approvedEmails';
  static const String settingsCollection = 'settings';

  // User Roles
  static const String superAdminRole = 'super_admin';
  static const String adminRole = 'admin';
  static const String userRole = 'user';

  // User Status
  static const String activeStatus = 'active';
  static const String inactiveStatus = 'inactive';
  static const String deletedStatus = 'deleted';

  // File Status
  static const String pendingReviewStatus = 'PENDING_REVIEW';
  static const String approvedStatus = 'APPROVED';
  static const String rejectedStatus = 'REJECTED';
  static const String revisionRequestedStatus = 'REVISION_REQUESTED';

  // Authentication Methods
  static const String emailAuthMethod = 'email';
  static const String googleAuthMethod = 'google';

  // File Constraints
  static const int maxFileSize = 500 * 1024 * 1024; // 500MB
  static const List<String> allowedFileTypes = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp',
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    'mp3',
    'wav',
    'aac',
    'flac',
    'ogg',
    'txt',
    'rtf',
    'csv',
    'zip',
    'rar',
    '7z',
  ];

  // Pagination
  static const int defaultPageSize = 20;
  static const int activitiesPageSize = 50;

  // Session Management
  static const Duration sessionTimeout = Duration(hours: 24);
  static const Duration rememberMeDuration = Duration(days: 30);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  // Responsive breakpoints
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Error Messages
  static const String genericErrorMessage =
      'Terjadi kesalahan. Silakan coba lagi.';
  static const String networkErrorMessage =
      'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
  static const String unauthorizedErrorMessage =
      'Anda tidak memiliki akses untuk melakukan aksi ini.';
  static const String fileNotFoundErrorMessage = 'File tidak ditemukan.';
  static const String fileSizeExceededErrorMessage =
      'Ukuran file melebihi batas maksimal 500MB.';
  static const String fileTypeNotAllowedErrorMessage =
      'Tipe file tidak diizinkan.';

  // Success Messages
  static const String fileUploadedSuccessMessage =
      'File berhasil diunggah dan menunggu persetujuan admin.';
  static const String fileApprovedSuccessMessage = 'File berhasil disetujui.';
  static const String fileRejectedSuccessMessage = 'File berhasil ditolak.';
  static const String userCreatedSuccessMessage = 'Pengguna berhasil dibuat.';
  static const String profileUpdatedSuccessMessage =
      'Profil berhasil diperbarui.';

  // Storage Paths
  static const String userAvatarsPath = 'user_avatars';
  static const String filesPath = 'files';
  static const String thumbnailsPath = 'thumbnails';

  // Notification Types
  static const String fileUploadNotification = 'file_upload';
  static const String fileApprovalNotification = 'file_approval';
  static const String userCreatedNotification = 'user_created';
  static const String systemNotification = 'system';

  // Activity Types
  static const String loginActivity = 'login';
  static const String logoutActivity = 'logout';
  static const String fileUploadActivity = 'file_upload';
  static const String fileApproveActivity = 'file_approve';
  static const String fileRejectActivity = 'file_reject';
  static const String userCreateActivity = 'user_create';
  static const String userUpdateActivity = 'user_update';
  static const String categoryCreateActivity = 'category_create';
  static const String categoryUpdateActivity = 'category_update';

  // Shared Preferences Keys
  static const String userPrefsKey = 'user_prefs';
  static const String themePrefsKey = 'theme_prefs';
  static const String languagePrefsKey = 'language_prefs';
  static const String rememberMeKey = 'remember_me';
  static const String lastLoginKey = 'last_login';

  // API Endpoints (for Cloud Functions)
  static const String createUserEndpoint = 'createUserByAdmin';
  static const String logActivityEndpoint = 'logActivity';
  static const String generateShareLinkEndpoint = 'generateShareableLink';
  static const String bulkImportUsersEndpoint = 'bulkImportUsers';

  // Regular Expressions
  static const String emailRegex =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';

  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String timeFormat = 'HH:mm';

  // Localization
  static const String defaultLocale = 'id';
  static const String fallbackLocale = 'en';

  // Feature Flags
  static const bool enableOfflineMode = false;
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
}
